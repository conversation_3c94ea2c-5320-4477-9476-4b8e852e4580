<template>
  <div class="Bt1">
    <div id="pieChart" class="pie-chart"></div>
    <div class="Bt1Img1"></div>
    <div class="Bt1Img2"></div>
    <div class="Bt1Img3"></div>
    <!-- 添加重播动画按钮 -->
    <button class="animation-trigger-btn" @click="triggerAnimation">🎬 重播动画</button>
  </div>
</template>

<script setup>
import * as echarts from "echarts";
import { onMounted, onUnmounted, computed, watch } from "vue";
import { usedata } from "../store/data";
const dataStore = usedata();

let myChart = null;

// 计算属性：从 dataStore 获取动态数据
const chartData = computed(() => {
  const alarmRisk = dataStore.data?.alarmRisk;
  if (!alarmRisk) {
    // 如果数据还未加载，返回默认值
    return [
      { value: 0, name: "高危攻击", itemStyle: { color: "#FD9391" } },
      { value: 0, name: "低危攻击", itemStyle: { color: "#32FEFC" } },
      { value: 0, name: "中危攻击", itemStyle: { color: "#95C1FA" } },
    ];
  }

  return [
    { value: alarmRisk.highNum || 0, name: "高危攻击", itemStyle: { color: "#FD9391" } },
    { value: alarmRisk.lowNum || 0, name: "低危攻击", itemStyle: { color: "#32FEFC" } },
    { value: alarmRisk.middleNum || 0, name: "中危攻击", itemStyle: { color: "#95C1FA" } },
  ];
});

const initChart = () => {
  const chartDom = document.getElementById("pieChart");
  if (chartDom) {
    myChart = echarts.init(chartDom);

    // 先显示空的图表，然后延迟显示数据以触发动画
    const emptyOption = {
      animation: true,
      animationDuration: 1000,
      animationEasing: "cubicOut",
      tooltip: {
        trigger: "item",
        formatter: "{a} <br/>{b}: {c} ({d}%)",
      },
      series: [
        {
          name: "数据统计",
          type: "pie",
          radius: ["30%", "36%"],
          data: [], // 空数据
        },
      ],
    };

    myChart.setOption(emptyOption);

    // 延迟500ms后显示真实数据，触发动画
    setTimeout(() => {
      updateChart();
    }, 500);

    // 监听窗口大小变化
    window.addEventListener("resize", () => {
      myChart && myChart.resize();
    });
  }
};

const updateChart = () => {
  if (!myChart) return;

  const option = {
    // 全局动画配置
    animation: true,
    animationDuration: 1000,
    animationEasing: "cubicOut",
    animationDelay: function (idx) {
      return idx * 100;
    },
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)",
    },
    series: [
      {
        name: "数据统计",
        type: "pie",
        radius: ["30%", "36%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        // 饼图特定动画配置
        animationType: "scale",
        animationEasing: "elasticOut",
        animationDelay: function (idx) {
          return idx * 100 + Math.random() * 100;
        },
        itemStyle: {
          borderRadius: 0,
        },
        label: {
          show: true,
          position: "outside",
          fontSize: 12,
          color: "#ffffff",
          formatter: function (params) {
            return `{name|${params.name}}\n{value|${params.value}}`;
          },
          fontWeight: "normal",
          rich: {
            name: {
              fontSize: 12,
              color: "#a1a1a1",
              fontWeight: "normal",
              lineHeight: 16,
              align: "center",
            },
            value: {
              fontSize: 20,
              color: "#ffffff",
              fontWeight: "bold",
              lineHeight: 20,
              align: "center",
              padding: [18, 0, 0, 0],
            },
          },
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: "bold",
            formatter: function (params) {
              return `{name|${params.name}}\n{value|${params.value}}`;
            },
            rich: {
              name: {
                fontSize: 14,
                color: "#ffffff",
                fontWeight: "bold",
                lineHeight: 18,
                align: "center",
              },
              value: {
                fontSize: 16,
                color: "#32FEFC",
                fontWeight: "bold",
                lineHeight: 22,
                align: "center",
                padding: [8, 0, 0, 0],
              },
            },
          },
        },
        labelLine: {
          show: true,
          length: 20,
          length2: 20,
          lineStyle: {
            width: 1,
            color: "#ffffff",
          },
        },
        data: chartData.value,
      },
    ],
  };

  myChart.setOption(option);
};

// 手动触发动画的方法
const triggerAnimation = () => {
  if (!myChart) return;

  // 先清空数据
  myChart.setOption({
    series: [
      {
        data: [],
      },
    ],
  });

  // 延迟后重新设置数据，触发动画
  setTimeout(() => {
    updateChart();
  }, 100);
};

// 监听数据变化，自动更新图表
watch(
  chartData,
  () => {
    updateChart();
  },
  { deep: true }
);

// 暴露方法供外部调用（可选）
defineExpose({
  triggerAnimation,
});

onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
  window.removeEventListener("resize", () => {
    myChart && myChart.resize();
  });
});
</script>

<style lang="less" scoped>
.Bt1 {
  width: 100%;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  pointer-events: all;
}

.pie-chart {
  width: 100%;
  height: 100%;
  min-height: 300px;
}
.Bt1Img1 {
  position: absolute;
  width: 58%;
  left: 97px;
  height: 118%;
  background-image: url("../../public/img/w.png");
  background-repeat: no-repeat;
  background-size: contain;
  pointer-events: none;
}
.Bt1Img2 {
  position: absolute;
  width: 49%;
  height: 117%;
  background-image: url("../../public/img/w2.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  animation: rotate-medium 15s linear infinite;
  pointer-events: none;
}
.Bt1Img3 {
  position: absolute;
  width: 43%;
  height: 107%;
  background-image: url("../../public/img/w3.png");
  background-repeat: no-repeat;
  background-size: contain;
  animation: rotate-fast 10s linear infinite;
  pointer-events: none;
}

@keyframes rotate-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes rotate-medium {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes rotate-fast {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

.animation-trigger-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(50, 254, 252, 0.2);
  border: 1px solid #32fefc;
  color: #32fefc;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
  z-index: 10;
}

.animation-trigger-btn:hover {
  background: rgba(50, 254, 252, 0.3);
  transform: scale(1.05);
}
</style>
